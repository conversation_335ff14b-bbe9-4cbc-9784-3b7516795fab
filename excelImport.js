// Excel 批次匯入功能

// 下載 Excel 範本
async function downloadExcelTemplate() {
    try {
        // 建立範本資料
        const templateData = [
            {
                '交易類型': '支出',
                '資金帳戶': '',
                '帳款到帳情形': '同日收款',
                '收款/付款日期': '2024-03-14',
                '憑證/發票日期': '2024-03-14',
                '預計收/付款日期': '',
                '交易項目': '',
                '交易對象': '',
                '金額': '0.00',
                '稅別': '應稅',
                '稅額': '0.00',
                '手續費': '否',
                '手續費金額': '0.00',
                '備註': '',
                '標籤': '',
                '狀態': '已完成'
            }
        ];

        // 建立工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);

        // 設定欄寬
        const colWidths = [
            { wch: 10 }, // 交易類型
            { wch: 20 }, // 資金帳戶
            { wch: 15 }, // 帳款到帳情形
            { wch: 12 }, // 收款/付款日期
            { wch: 12 }, // 憑證/發票日期
            { wch: 12 }, // 預計收/付款日期
            { wch: 20 }, // 交易項目
            { wch: 20 }, // 交易對象
            { wch: 10 }, // 金額
            { wch: 10 }, // 稅別
            { wch: 10 }, // 稅額
            { wch: 10 }, // 手續費
            { wch: 12 }, // 手續費金額
            { wch: 30 }, // 備註
            { wch: 20 }, // 標籤
            { wch: 10 }  // 狀態
        ];
        ws['!cols'] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "交易記錄範本");

        // 產生 Excel 檔案並下載
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '交易記錄匯入範本.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('產生範本失敗:', error);
        alert('產生範本失敗: ' + error.message);
    }
}



//匯出Excel
async function exportExcel(data) {
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(data);

    // 計算每一欄最大寬度
    const cols = Object.keys(data[0] || {});
    const colWidths = cols.map(key => {
        // 取得該欄所有內容的長度（含標題）
        const maxLen = Math.max(
            key.length,
            ...data.map(row => (row[key] ? String(row[key]).length : 0))
        );
        // 適度加大一點寬度
        return { wch: maxLen + 10 };
    });
    ws['!cols'] = colWidths;

    XLSX.utils.book_append_sheet(wb, ws, "交易記錄");
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    //交易紀錄時間包含至yyyy-mm-dd-hh-mm
    const now = new Date();
    const dateStr = CommonUtils.formatDate(now, 'YYYY-MM-DD');
    const timeStr = `${now.getHours()}-${now.getMinutes()}`;
    link.download = `交易記錄${dateStr}-${timeStr}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}


// 獲取帳戶列表
async function getAccounts() {
    try {
        const accounts = await getAllData(STORE_NAMES.accounts);
        return accounts;
    } catch (error) {
        console.error('獲取帳戶列表失敗:', error);
        throw new Error('獲取帳戶列表失敗');
    }
}

// 獲取交易對象列表
async function getEntities() {
    try {
        const entities = await getAllData(STORE_NAMES.entities);
        return entities;
    } catch (error) {
        console.error('獲取交易對象列表失敗:', error);
        throw new Error('獲取交易對象列表失敗');
    }
}

// 獲取稅別列表
async function getTaxTypesRates() {
    try {
        const taxTypesRates = await getAllData(STORE_NAMES.taxTypesRates);
        return taxTypesRates;
    } catch (error) {
        console.error('獲取稅別列表失敗:', error);
        throw new Error('獲取稅別列表失敗');
    }
}

// 處理 Excel 檔案上傳
async function handleExcelUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
        const data = await readExcelFile(file);
        const { transactions, unmatchedItems } = await processExcelData(data);
        
        if (unmatchedItems.length > 0) {
            // 顯示對話框並等待使用者選擇
            await showUnmatchedItemsDialog(unmatchedItems);
            // 使用更新後的 unmatchedItems 重新處理資料
            const updatedTransactions = await updateTransactionsWithSelections(data, unmatchedItems);
            // 顯示確認對話框
            if (await showImportConfirmationDialog(updatedTransactions)) {
                await saveTransactions(updatedTransactions);
                alert('交易記錄匯入成功！');
                window.location.reload();
            }
        } else {
            // 顯示確認對話框
            if (await showImportConfirmationDialog(transactions)) {
                await saveTransactions(transactions);
                alert('交易記錄匯入成功！');
                window.location.reload();
            }
        }
    } catch (error) {
        console.error('匯入失敗:', error);
        alert('匯入失敗: ' + error.message);
    }
}

// 讀取 Excel 檔案
function readExcelFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(firstSheet);
                resolve(jsonData);
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = (error) => reject(error);
        reader.readAsArrayBuffer(file);
    });
}

// 處理 Excel 資料
async function processExcelData(data) {
    const accounts = await getAccounts();
    const entities = await getEntities();
    const taxTypesRates = await getTaxTypesRates();

    const accountMap = new Map(accounts.map(acc => [acc.name, acc.id]));
    const entityMap = new Map(entities.map(ent => [ent.name, { id: ent.id, type: ent.type }]));
    const taxTypeMap = new Map(taxTypesRates.map(tax => [tax.name, {id: tax.id, rate: tax.rate}]));

    const unmatchedItems = [];
    const transactions = data.map((row, index) => {
        const accountName = row['資金帳戶'];
        const entityName = row['交易對象'];
        const taxTypeName = row['稅別'];
        const accountId = accountMap.get(accountName);
        const entityInfo = entityMap.get(entityName);
        const taxType = taxTypeMap.get(taxTypeName);
        
        if (!accountId || !entityInfo || !taxType) {
            unmatchedItems.push({
                rowIndex: index + 2, // Excel 行號從 2 開始
                accountName,
                entityName,
                accountId,
                entityId: entityInfo?.id,
                entityType: entityInfo?.type,
                taxTypeName: taxTypeName,
                taxTypeId: taxType?.id,
                taxRate: taxType?.rate
            });
        }
        
        return {
            // 基本資訊
            transactionType: mapTransactionType(row['交易類型']),
            accountId: accountId || '',
            paymentStatus: mapPaymentStatus(row['帳款到帳情形']),
            paymentDate: formatDate(row['收款/付款日期']),
            invoiceDate: formatDate(row['憑證/發票日期']),
            expectedPaymentDate: formatDate(row['預計收/付款日期']),
            
            // 交易項目
            paymentDescription: Number(row['交易項目'] || ''),
            
            // 交易對象
            entityId: entityInfo?.id || '',
            entityType: entityInfo?.type || '',
            
            // 金額相關
            amount: parseFloat(row['金額']) || 0,
            taxTypeId: taxType?.id || '',
            taxAmount: taxType ? taxType.rate * parseFloat(row['金額']) : 0,
            fee: parseFloat(row['手續費金額']) || 0,
            
            // 其他資訊
            notes: row['備註'] || '',
            tags: row['標籤'] ? row['標籤'].split(',').map(tag => tag.trim()) : [],
            transactionStatus: mapTransactionStatus(row['狀態'])
        };
    });
    
    return { transactions, unmatchedItems };
}

// 顯示未匹配項目對話框
async function showUnmatchedItemsDialog(unmatchedItems) {
    const accounts = await getAccounts();
    const entities = await getEntities();
    const taxTypesRates = await getTaxTypesRates();

    return new Promise((resolve) => {
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full';
        dialog.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">請選擇對應的帳戶和交易對象</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">行號</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">資金帳戶</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">選擇帳戶</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易對象</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">選擇交易對象</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">稅別</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">選擇稅別</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${unmatchedItems.map(item => `
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">${item.rowIndex}</td>
                                        <td class="px-6 py-4 whitespace-nowrap ${item.accountName ? 'text-gray-500' : 'text-yellow-500'}">${item.accountName||'未設定'}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <select class="account-select rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                <option value="">請選擇</option>
                                                ${accounts.map(acc => `
                                                    <option value="${acc.id}" ${acc.name === item.accountName ? 'selected' : ''}>
                                                        ${acc.name}
                                                    </option>
                                                `).join('')}
                                            </select>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap ${item.entityName ? 'text-gray-500' : 'text-yellow-500'}">${item.entityName||'未設定'}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <select class="entity-select rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                <option value="">請選擇</option>
                                                ${entities.sort((a, b) => a.type.localeCompare(b.type)).map(ent => `
                                                    <option value="${ent.id}" data-type="${ent.type}" ${ent.name === item.entityName ? 'selected' : ''}>
                                                        ${ent.type}-${ent.name}
                                                    </option>
                                                `).join('')}
                                            </select>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap ${item.taxTypeName ? 'text-gray-500' : 'text-yellow-500'}">${item.taxTypeName||'未設定'}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <select class="tax-select rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                                <option value="">請選擇</option>
                                                ${taxTypesRates.map(tax => `
                                                    <option value="${tax.id}" data-rate="${tax.rate}" ${tax.name === item.taxTypeName ? 'selected' : ''}>
                                                        ${tax.name}
                                                    </option>
                                                `).join('')}
                                            </select>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button id="confirmBtn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            確認
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        const confirmBtn = dialog.querySelector('#confirmBtn');
        confirmBtn.addEventListener('click', () => {
            const accountSelects = dialog.querySelectorAll('.account-select');
            const entitySelects = dialog.querySelectorAll('.entity-select');
            const taxSelects = dialog.querySelectorAll('.tax-select');
            
            unmatchedItems.forEach((item, index) => {
                const accountId = accountSelects[index].value;
                const entitySelect = entitySelects[index];
                const entityId = entitySelect.value;
                const entityType = entitySelect.options[entitySelect.selectedIndex].dataset.type;
                const taxSelect = taxSelects[index];
                const taxId = taxSelect.value;
                const taxRate = taxSelect.options[taxSelect.selectedIndex].dataset.rate;
                if (accountId) {
                    item.accountId = accountId;
                }
                if (entityId) {
                    item.entityId = entityId;
                    item.entityType = entityType;
                }
                if (taxId) {
                    item.taxTypeId = taxId;
                    item.taxRate = taxRate;
                }
            });
            
            document.body.removeChild(dialog);
            resolve();
        });
    });
}

// formatDate 函數已移至 CommonUtils.js 統一管理
// 保留 Excel 專用的空值處理邏輯
function formatDate(dateStr) {
    if (!dateStr) return null;

    // 使用 CommonUtils 的增強版 formatDate，它已支援 Excel 日期處理
    const result = CommonUtils.formatDate(dateStr, 'YYYY-MM-DD');
    return result || null;
}

// 映射交易類型
function mapTransactionType(type) {
    const typeMap = {
        '收入': 'income',
        '支出': 'expense',
        '轉移': 'transfer'
    };
    return typeMap[type] || 'expense';
}

// 映射帳款到帳情形
function mapPaymentStatus(status) {
    const statusMap = {
        '同日收款': 'same_day',
        '應收款': 'receivable',
        '暫收款': 'temporary',
        '非同日收款': 'different_day'
    };
    return statusMap[status] || 'same_day';
}


// 映射交易狀態
function mapTransactionStatus(status) {
    const statusMap = {
        '已完成': 'completed',
        '未撥款': 'pending'
    };
    return statusMap[status] || 'completed';
}

// 儲存交易記錄
async function saveTransactions(transactionsData) {
    try {
        let savedTransactionId;
        for (const transaction of transactionsData) {
            transaction.createdAt = new Date().toISOString();
            savedTransactionId = await addData(STORE_NAMES.transactions, transaction);
        }
    } catch (error) {
        throw new Error('儲存交易記錄失敗: ' + error.message);
    }
}

// 使用選擇結果更新交易資料
async function updateTransactionsWithSelections(data, unmatchedItems) {
    const accounts = await getAccounts();
    const entities = await getEntities();
    const taxTypesRates = await getTaxTypesRates();
    
    const accountMap = new Map(accounts.map(acc => [acc.name, acc.id]));
    const entityMap = new Map(entities.map(ent => [ent.name, { id: ent.id, type: ent.type }]));
    const taxTypeMap = new Map(taxTypesRates.map(tax => [tax.name, {id: tax.id, rate: tax.rate}]));
    // 建立未匹配項目的對應表
    const unmatchedMap = new Map(unmatchedItems.map(item => [
        item.rowIndex,
        { accountId: item.accountId, entityId: item.entityId, entityType: item.entityType, taxTypeId: item.taxTypeId ,taxRate: item.taxRate}
    ]));
    
    return data.map((row, index) => {
        const rowIndex = index + 2; // Excel 行號從 2 開始
        const unmatchedInfo = unmatchedMap.get(rowIndex);
        
        // 如果有未匹配項目的選擇結果，使用選擇的值
        const accountId = unmatchedInfo?.accountId || accountMap.get(row['資金帳戶']) || '';
        const entityInfo = unmatchedInfo ? 
            { id: unmatchedInfo.entityId, type: unmatchedInfo.entityType } : 
            entityMap.get(row['交易對象']);
        const taxTypeId = unmatchedInfo ? unmatchedInfo.taxTypeId: taxTypeMap.get(row['稅別']);
        const texRate =  unmatchedInfo ? unmatchedInfo.taxRate : 0 ;

        return {
            // 基本資訊
            transactionType: mapTransactionType(row['交易類型']),
            accountId,
            paymentStatus: mapPaymentStatus(row['帳款到帳情形']),
            paymentDate: formatDate(row['收款/付款日期']),
            invoiceDate: formatDate(row['憑證/發票日期']),
            expectedPaymentDate: formatDate(row['預計收/付款日期']),
            
            // 交易項目
            paymentDescription: Number(row['交易項目'] || ''),
            
            // 交易對象
            entityId: entityInfo?.id || '',
            entityType: entityInfo?.type || '',
            
            // 金額相關
            amount: parseFloat(row['金額']) || 0,
            taxTypeId: taxTypeId || '',
            taxAmount: texRate ? texRate * parseFloat(row['金額']) : 0,
            fee: parseFloat(row['手續費金額']) || 0,
            
            // 其他資訊
            notes: row['備註'] || '',
            tags: row['標籤'] ? row['標籤'].split(',').map(tag => tag.trim()) : [],
            transactionStatus: mapTransactionStatus(row['狀態'])
        };
    });
}

// 顯示匯入確認對話框
async function showImportConfirmationDialog(transactions) {
    return new Promise((resolve) => {
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full';
        
        // 計算各類型的數量
        const typeCounts = transactions.reduce((acc, t) => {
            acc[t.transactionType] = (acc[t.transactionType] || 0) + 1;
            return acc;
        }, {});
        
        // 計算總金額
        const totalIncomeAmount = transactions.reduce((sum, t) => sum + (t.transactionType === 'income' ? t.amount : 0), 0);
        const totalExpenseAmount = transactions.reduce((sum, t) => sum + (t.transactionType === 'expense' ? t.amount : 0), 0);
        
        dialog.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">確認匯入交易記錄</h3>
                    
                    <!-- 摘要資訊 -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">總筆數</p>
                                <p class="text-lg font-semibold">${transactions.length} 筆</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">總金額</p>
                                <p class="text-lg font-semibold">$${(totalIncomeAmount-totalExpenseAmount).toLocaleString()}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm font-medium text-gray-500">交易類型分布</p>
                            <div class="mt-2 space-y-2">
                                ${Object.entries(typeCounts).map(([type, count]) => `
                                    <div class="flex justify-between">
                                        <span class="text-sm">${mapTransactionTypeToText(type)}</span>
                                        <span class="text-sm font-medium">${count} 筆</span>
                                        <span class="text-sm font-medium">$${type === 'income' ? totalIncomeAmount.toLocaleString() : totalExpenseAmount.toLocaleString()}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 詳細資料表格 -->
                    <div class="overflow-x-auto max-h-96">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易類型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">日期</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易對象</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">金額</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">會計科目</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">備註</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">狀態</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${transactions.map(t => `
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                                ${t.transactionType === 'income' ? 'bg-green-100 text-green-800' : 
                                                  t.transactionType === 'expense' ? 'bg-red-100 text-red-800' : 
                                                  'bg-blue-100 text-blue-800'}">
                                                ${mapTransactionTypeToText(t.transactionType)}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${t.paymentDate}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${t.entityId ? '已選擇' : '未選擇'}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            $${t.amount.toLocaleString()}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${t.paymentDescription}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${t.notes}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                                ${t.transactionStatus === 'completed' ? 'bg-green-100 text-green-800' : 
                                                  'bg-yellow-100 text-yellow-800'}">
                                                ${mapTransactionStatusToText(t.transactionStatus)}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 按鈕 -->
                    <div class="mt-4 flex justify-end space-x-3">
                        <button id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                        <button id="confirmBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            確認匯入
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        const confirmBtn = dialog.querySelector('#confirmBtn');
        const cancelBtn = dialog.querySelector('#cancelBtn');
        
        confirmBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(true);
        });
        
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(false);
        });
    });
}

// 映射交易類型到顯示文字
function mapTransactionTypeToText(type) {
    const typeMap = {
        'income': '收入',
        'expense': '支出',
        'transfer': '轉移'
    };
    return typeMap[type] || type;
}

// 映射交易狀態到顯示文字
function mapTransactionStatusToText(status) {
    const statusMap = {
        'completed': '已完成',
        'pending': '未撥款'
    };
    return statusMap[status] || status;
}

// 初始化上傳功能
function initExcelUpload() {
    const fileInput = document.getElementById('excelFileInput');
    const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');
    
    if (fileInput) {
        fileInput.addEventListener('change', handleExcelUpload);
    }
    
    if (downloadTemplateBtn) {
        downloadTemplateBtn.addEventListener('click', downloadExcelTemplate);
    }
}

// 當 DOM 載入完成時初始化
document.addEventListener('DOMContentLoaded', initExcelUpload); 