<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易記錄 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 模組導入映射設定 -->
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>

    <!-- 樣式庫載入 -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script> <!-- Tailwind CSS 框架 -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <!-- Font Awesome 圖示庫 -->

    <!-- Firebase 相關庫載入 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script> <!-- Firebase 核心 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script> <!-- Firebase 分析 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script> <!-- Firebase 資料庫 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script> <!-- Firebase 認證 -->

    <!-- Excel 相關庫載入 -->
    <script src="../../common/lib/xlsx.full.min.js"></script>

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script> <!-- 認證相關功能 -->
    <script src="../../common/db/db.js"></script> <!-- 資料庫操作功能 -->
    <script src="../../common/db/preload.js"></script> <!-- 資料預載功能 -->
    <script src="../../common/utils/CommonUtils.js"></script> <!-- 通用工具函數 -->
    <script src="../../common/utils/pageTransfer.js"></script> <!-- 頁面轉換工具 -->
    <script src="../../common/utils/dataPaging.js"></script> <!-- 資料分頁工具函數 -->
    <script src="../../common/utils/ModalUtils.js"></script> <!-- 模態框工具 -->
    <script src="../../common/utils/DatabaseErrors.js"></script> <!-- 資料庫錯誤處理 -->

    <!-- 自定義樣式 -->
    <style>
        /* 導航選單懸停效果 */
        .nav-item:hover>.submenu {
            display: block; /* 懸停時顯示子選單 */
        }

        /* 子選單基本樣式 */
        .submenu {
            display: none; /* 預設隱藏 */
            position: absolute; /* 絕對定位 */
            background-color: white; /* 白色背景 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 陰影效果 */
            z-index: 1000; /* 層級設定 */
            top: 100%; /* 位於父元素下方 */
            left: 0; /* 左對齊 */
        }

        /* 多層子選單定位 */
        .submenu .submenu {
            top: 0; /* 與父選單同高 */
            left: 100%; /* 位於父選單右側 */
        }

        /* 導航項目相對定位 */
        .nav-item {
            position: relative; /* 相對定位以支援子選單 */
        }

        /* 標籤點擊效果 */
        .tag-clickable {
            transition: all 0.2s ease-in-out;
        }

        .tag-clickable:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tag-clickable:active {
            transform: translateY(0);
        }

        /* 選中標籤的特殊樣式 */
        .tag-selected {
            box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
        }
    </style>

    <!-- 導航功能載入 -->
    <script src="../../common/navigation/navigation.js"></script>
</head>
<body class="flex flex-col min-h-screen bg-gray-100">

    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">交易記錄</h1>
            <p class="text-gray-600">查看所有財務交易記錄</p>
        </div>

        <!-- 篩選器 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <form id="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">交易類型</label>
                    <select id="typeFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="income">收入</option>
                        <option value="expense">支出</option>
                        <option value="transfer">轉移</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">起始日期</label>
                    <input type="date" id="startDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">結束日期</label>
                    <input type="date" id="endDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        搜尋
                    </button>
                </div>
            </form>

            <!-- 標籤過濾區域 -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex flex-wrap items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">標籤過濾：</label>
                    <div id="selectedTagsContainer" class="flex flex-wrap gap-2">
                        <!-- 選中的標籤將在這裡顯示 -->
                    </div>
                    <button type="button" id="clearTagsBtn" class="text-sm text-red-600 hover:text-red-800 hidden">
                        <i class="fas fa-times mr-1"></i>清除所有標籤
                    </button>
                </div>
                <div class="mt-2 text-sm text-gray-500" id="filterResultInfo">
                    <!-- 過濾結果資訊將在這裡顯示 -->
                </div>
            </div>
        </div>

        <!-- 交易列表 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800">交易記錄列表</h2>
                <div class="flex space-x-3">
                    <button id="downloadTemplateBtn" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                        <span class="hidden md:block">下載範本</span>
                        <span class="block md:hidden"><i class="fas fa-download mr-1"></i>範本</span>
                    </button>
                    <label class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 cursor-pointer">
                        <span class="hidden md:block">匯入Excel</span>
                        <span class="block md:hidden"><i class="fas fa-file-import mr-1"></i>匯入</span>
                        <input type="file" id="excelFileInput" accept=".xlsx,.xls" class="hidden">
                    </label>
                    <button onclick="exportTransactions()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <span class="hidden md:block">匯出報表</span>
                        <span class="block md:hidden"><i class="fas fa-file-export mr-1"></i>匯出</span>
                    </button>
                    <a href="transaction-create.html" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        <span class="hidden md:block">新增交易</span>
                        <span class="block md:hidden"><i class="fas fa-plus mr-1"></i>新增</span>
                    </a>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易類型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易項目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金額</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">帳戶</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易對象</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">標籤</th>
                            <!--th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">狀態</!th-->
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="transactionTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 資料將由 JavaScript 動態填充 -->
                    </tbody>
                </table>
            </div>
            <!-- 分頁控制 -->
            <div class="mt-4">
                <div class="flex items-center justify-between text-sm text-gray-600">
                    <button id="prevPage" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50">
                        <i class="fas fa-chevron-left mr-1"></i>上一頁
                    </button>
                    <span id="pageInfo" class="text-gray-600">第 1 頁，共 1 頁</span>
                    <button id="nextPage" class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50">
                        下一頁<i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white shadow-lg mt-auto">
        <div class="container mx-auto px-4 py-6">
            <p class="text-center text-gray-600">© 2024 益芯能源工程-管理系統. All rights reserved.</p>
        </div>
    </footer>
    <script src="transactionList.js"></script>
    <script src="excelImport.js"></script>
</body>
</html>