// 分頁設定
const PAGE_SIZE = 20;//每頁顯示5筆交易
let dataPaging = null;

let filteredTransactions = [];
let selectedTags = []; // 儲存選中的標籤

// 初始化頁面
document.addEventListener('DOMContentLoaded', async () => {
    // 綁定篩選表單事件
    document.getElementById('filterForm').addEventListener('submit', handleFilter);

    // 綁定分頁按鈕事件
    document.getElementById('prevPage').addEventListener('click', () => changePage(-1));
    document.getElementById('nextPage').addEventListener('click', () => changePage(1));

    // 綁定清除標籤按鈕事件
    document.getElementById('clearTagsBtn').addEventListener('click', clearAllTagFilters);

    // 載入初始資料
    await loadTransactions();
});

// 載入交易資料
async function loadTransactions(filters = {}) {
    try {
        // 從資料庫獲取所有交易
        let transactions = await getAllData(STORE_NAMES.transactions);
        
        // 應用篩選條件
        filteredTransactions = applyFilters(transactions, filters);

        //分頁前先排序，依照paymentDate排序，paymentDate為空則依照expectedPaymentDate排序
        filteredTransactions.sort((a, b) => {
            const dateA = a.paymentDate || a.expectedPaymentDate;
            const dateB = b.paymentDate || b.expectedPaymentDate;
            return dateA ? new Date(dateA).getTime() - new Date(dateB).getTime() : new Date(dateB).getTime() - new Date(dateA).getTime();
        });

        //依照目前資料建立分頁器
        dataPaging = new DataPaging(filteredTransactions, PAGE_SIZE);
        
        //頁面定向
        const urlParams = new URLSearchParams(window.location.search);
        if(urlParams.get('page')){
            const pageNum=Number(urlParams.get('page'));
            dataPaging.setCurrentPage(pageNum);
        }

        // 更新顯示
        updateDisplay();
    } catch (error) {
        console.error('載入交易資料失敗：', error);
        alert('載入資料失敗，請重試');
    }
}

// 應用篩選條件
function applyFilters(transactions, filters) {
    return transactions.filter(transaction => {
        // 交易類型篩選
        if (filters.type && transaction.transactionType !== filters.type) {
            return false;
        }

        // 日期範圍篩選
        const dateToCheck = transaction.paymentDate || transaction.expectedPaymentDate;
        if (!dateToCheck) return true;  // 如果兩個日期都不存在，仍然顯示

        if (filters.startDate) {
            const start = new Date(filters.startDate);
            const transactionDate = new Date(dateToCheck);
            if (transactionDate < start) return false;
        }

        if (filters.endDate) {
            const end = new Date(filters.endDate);
            const transactionDate = new Date(dateToCheck);
            if (transactionDate > end) return false;
        }

        // 標籤篩選
        if (selectedTags.length > 0) {
            // 檢查交易是否包含任何選中的標籤
            if (!transaction.tags || transaction.tags.length === 0) {
                return false;
            }

            // 檢查是否有任何選中的標籤存在於交易的標籤中
            const hasMatchingTag = selectedTags.some(selectedTag =>
                transaction.tags.includes(selectedTag)
            );

            if (!hasMatchingTag) {
                return false;
            }
        }

        return true;
    });
}

// 處理篩選表單提交
async function handleFilter(event) {
    event.preventDefault();
    
    const filters = {
        type: document.getElementById('typeFilter').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value
    };
    
    await loadTransactions(filters);
}

// 取得交易描述的完整顯示文字
async function getTransactionDescription(accountingCode, transactionType) {
    if (!accountingCode) return '無';
    
    try {
        const categories = await getAllData(STORE_NAMES.transactionCategories);
        const items = await getAllData(STORE_NAMES.transactionCategoryItems);
        const accountingItems = await getAllData(STORE_NAMES.accountingItems);
        
        // 找到對應的會計項目
        const item = items.find(item => item.accountingCode === accountingCode);
        if (item) {
            // 找到所屬分類
            const category = categories.find(cat => cat.id == item.categoryId && cat.type === transactionType);
            if (category) {
                return `${category.name}/${item.accountingName}(${item.accountingCode})`;
            }
        }

        const accountingItem = accountingItems.find(item => String(item.code) === String(accountingCode));
        if (accountingItem) {
            if(transactionType === 'transfer'){
                return `轉移-${accountingItem.type}-${accountingItem.name}(${accountingItem.code})`;
            }else{
                return `${accountingItem.type}-${accountingItem.name}(${accountingItem.code})`;
            }
        } else {
             // 如果找不到對應項目，顯示編碼
            return accountingCode;
        }
        
    } catch (error) {
        console.error('處理交易描述時發生錯誤:', error);
        return accountingCode; // 發生錯誤時直接顯示原始代碼
    }
}

// 更新顯示
async function updateDisplay() {
    const tbody = document.getElementById('transactionTableBody');
    tbody.innerHTML = '';

    if(filteredTransactions.length === 0){
        tbody.innerHTML = `
        <tr>
            <td colspan="8" class="p-6 text-2xl text-center text-gray-500">
                無交易記錄
            </td>
        </tr>
        `;
        return;
    }

    // 取得目前頁面的資料
    const pageTransactions = dataPaging.getDataOfCurrentPage();
    
    // 更新表格資訊
    for (const transaction of pageTransactions) {
        const accountName = await getAccountName(transaction.accountId);
        const transactionDescription = await getTransactionDescription(Number(transaction.paymentDescription), transaction.transactionType);
        const entity = await getTargetEntityInfo(transaction);
        const row = document.createElement('tr');
        
        // 格式化金額
        const formattedAmount = new Intl.NumberFormat('zh-TW', {
            style: 'currency',
            currency: 'TWD',
            minimumFractionDigits: 0
        }).format(Math.abs(transaction.amount+(transaction.transactionType === 'income' ? -transaction.fee : transaction.fee)));

        // 處理日期顯示邏輯
        let displayDate = '';
        let dateNote = '';
        if(transaction.paymentDate && !transaction.invoiceDate){
            displayDate = transaction.paymentDate;
            dateNote = ' (未開立發票)';
        }else if (transaction.paymentDate) {
            displayDate = transaction.paymentDate;
        } else if (transaction.expectedPaymentDate) {
            displayDate = transaction.expectedPaymentDate;
            dateNote = ' (預計)';
        } else {
            displayDate = '未設定日期';
        }
        
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                ${displayDate}
                ${dateNote ? `<span class="text-xs text-gray-500">${dateNote}</span>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${transaction.transactionType === 'income' ? 'bg-green-100 text-green-800' : transaction.transactionType === 'expense' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                    ${transaction.transactionType === 'income' ? '收入' : transaction.transactionType === 'expense' ? '支出' : '轉移'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${transaction.notes ||transactionDescription}</td>
            <td class="px-6 py-4 whitespace-nowrap ${transaction.transactionType === 'income' ? 'text-green-600' : 'text-red-600'}">
                ${transaction.transactionType === 'income' ? '+' : '-'}${formattedAmount}
            </td>
            <td class="px-6 py-4 whitespace-nowrap"><span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700">${accountName}</span></td>
            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getEntityStyle(entity.type)}">${entity.name}(${entity.type})</span></td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-wrap gap-1">
                    ${
                        
                        transaction.tags && transaction.tags.length > 0
                        ? transaction.tags.map(tag => {
                            const tagColor = getTagColor(tag);
                            const isSelected = selectedTags.includes(tag);
                            return `
                            <span class="tag-clickable inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium cursor-pointer transition-all duration-200 hover:opacity-80 ${tagColor.textColor} ${tagColor.bgColor} ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}"
                                  data-tag="${tag}"
                                  title="點擊過濾此標籤">
                                ${tag}
                            </span>
                        `}).join('')
                        : '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">無標籤</span>'
                    }
                </div>
            </td>
            <!--td-- class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${getStatusStyle(transaction.transactionStatus)}">
                    ${getStatusText(transaction.transactionStatus)}
                </span>
            </td-->
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex space-x-2">
                    <button onclick="editTransaction('${transaction.id}')" 
                        class="text-blue-600 hover:text-blue-900">
                        編輯
                    </button>
                    <button onclick="deleteTransaction('${transaction.id}')" 
                        class="text-red-600 hover:text-red-900">
                        刪除
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    }
    
    // 更新分頁資訊
    updatePageInfo();

    // 綁定標籤點擊事件
    bindTagClickEvents();

    // 更新標籤過濾顯示
    updateTagFilterDisplay();
}
//--------------------------------分頁操作--------------------------------
// 切換頁面
function changePage(delta) {
    const newPage = dataPaging.getCurrentPage() + delta;
    if (newPage >= 1 && newPage <= dataPaging.getTotalPages()) {
        //更新URL參數page
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', newPage);
        window.history.replaceState({}, '', window.location.pathname + '?' + urlParams.toString());

        dataPaging.getDataOfPage(newPage);
        updateDisplay();
    }
}

//更新頁面資訊
function updatePageInfo() {
    document.getElementById('pageInfo').textContent = `第 ${dataPaging.getCurrentPage()} 頁，共 ${dataPaging.getTotalPages()} 頁`;
    document.getElementById('prevPage').disabled = dataPaging.getCurrentPage() === 1;
    document.getElementById('nextPage').disabled = dataPaging.getCurrentPage() === dataPaging.getTotalPages();
}
// 查詢員工名稱
async function getEmployeeName(employeeId) {
    const employee = await getDataById(STORE_NAMES.employees, employeeId);
    return employee ? employee.name : '未知員工';
}

// 取得帳戶名稱
async function getAccountName(accountId) {
    try {
        const account = await getDataById(STORE_NAMES.accounts, accountId);
        return account ? account.name : '未知帳戶';
    } catch (error) {
        console.error('獲取帳戶名稱失敗：', error);
        return '未知帳戶';
    }
}
// 查詢對象名稱
async function getEntity(entityId) {
    try {
        let storeName = STORE_NAMES.entities;
        
        const entity = await getDataById(storeName, entityId);


        return entity ;
    } catch (error) {

        console.error('查詢對象名稱時發生錯誤：', error);
        return '未知對象';
    }
}

// 取得交易對象資訊
async function getTargetEntityInfo(transaction) {
    let entityInfo;
    try {
        // 獲取帳戶和對象名稱
        let entityName;
        let entityType;
        if(transaction.entityType === 'account'){
            entityName = await getAccountName(transaction.entityId);
            entityType = '帳戶';
        }else if(transaction.entityType === 'employee'){
            entityName = await getEmployeeName(transaction.entityId);
            entityType = '員工';
        }else{
            if(transaction.entityId){
                const entity = await getEntity(transaction.entityId);
                if(entity){
                    entityName = entity.name;
                    entityType = getEntityTypeLabel(entity.type);
                }else{
                    entityName = '可能被已刪除';
                    entityType = '對象';
                }
            }else{
                entityName = '無設定';
                entityType = '對象';
            }
        }
        entityInfo = {
            type: entityType,
            name: entityName
        };
        return entityInfo;
    } catch (error) {
        console.error('取得交易對象資訊失敗：', error);
        return null;
    }
}
// 取得對象類型標籤
function getEntityTypeLabel(type) {
    switch (type) {
        case 'temporary':
            return '臨時對象';
        case 'client':
            return '客戶';
        case 'supplier':
            return '供應商';
        case 'account':
            return '帳戶';
        case 'employee':
            return '員工';
        default:
            return '其他';
    }
}
// 取得對象類型樣式
function getEntityStyle(type) {
    switch (type) {
        case '臨時對象':
            return 'bg-gray-100 text-gray-800';
        case '客戶':
            return 'bg-blue-100 text-blue-800';
        case '供應商':
            return 'bg-orange-100 text-orange-800';
        case '帳戶':
            return 'bg-green-100 text-green-800';
        case '員工':
            return 'bg-yellow-100 text-yellow-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

// 取得狀態樣式
function getStatusStyle(status) {
    switch (status) {
        case 'completed':
            return 'bg-green-100 text-green-800';
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'cancelled':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

// 取得狀態文字
function getStatusText(status) {
    switch (status) {
        case 'completed':
            return '已完成';
        case 'pending':
            return '待處理';
        case 'cancelled':
            return '已取消';
        default:
            return '未知';
    }
}

//取得標籤色彩(使用雜湊方式確保相同文字產生相同結果)
function getTagColor(tagName) {
    //文字色彩
  const colors = ['gray', 'red', 'yellow', 'green', 'blue', 'indigo', 'purple', 'pink', 'rose', 'teal', 'orange', 'lime', 'emerald', 'cyan', 'violet'];
  
  //文字色彩亮度
  const intensities = [900, 800, 700];


  // 簡單的雜湊函式（將字串轉為數值）
  let hash = 0;
  for (let i = 0; i < tagName.length; i++) {
    hash = tagName.charCodeAt(i) + ((hash << 5) - hash);
  }

  // 對 color 和 intensity 做索引映射
  const colorIndex = Math.abs(hash) % colors.length;
  const intensityIndex = Math.abs(hash >> 3) % intensities.length;

  //背景色彩亮度計算(為文字的反向)，限制於100-500之間且至少比背景差200
  const bgColorIntensity = Math.max(100, Math.min(900, 900 - intensities[intensityIndex] - 200)); //900-intensities[intensityIndex];
  const color={
    textColor: `text-${colors[colorIndex]}-${intensities[intensityIndex]}`,
    bgColor: `bg-${colors[colorIndex]}-${bgColorIntensity}`
  }
  return color;
}

// 編輯交易
function editTransaction(id) {
    window.location.href = `transaction-create.html?id=${id}`;
}

// 刪除交易
async function deleteTransaction(id) {
    if (!confirm('確定要刪除此筆交易嗎？')) {
        return;
    }
    
    try {
        await deleteData(STORE_NAMES.transactions, id);
        // 同時刪除相關的交易明細
        await deleteTransactionDetails(id);
        await loadTransactions();
        alert('交易已刪除');
    } catch (error) {
        console.error('刪除交易失敗：', error);
        alert('刪除失敗，請重試');
    }
}

//Tag 標籤陣列轉換成字串
function getTagsString(tags) {
    return tags.map(tag => tag).join(',');
}


// 匯出交易報表
async function exportTransactions() {
    // 這裡可以實作匯出功能
    //alert('匯出功能開發中');


    //取得稅別
    const taxTypes = await getAllData(STORE_NAMES.taxTypesRates);
    const taxTypeMap = new Map(taxTypes.map(tax => [tax.id, tax.name]));


    // 匯出前轉換成需要的格式
    const transactions = await Promise.all(filteredTransactions.map(async transaction => {
        const accountName = await getAccountName(transaction.accountId);
        const transactionDescription = await getTransactionDescription(Number(transaction.paymentDescription), transaction.transactionType);
        const entity = await getTargetEntityInfo(transaction);
        const taxTypeName = taxTypeMap.get(transaction.taxTypeId);
        //刪除不須要的欄位
        delete transaction.id;
        delete transaction.accountId;
        delete transaction.entityId;
        delete transaction.taxTypeId;
        delete transaction.createdAt;
        delete transaction.updatedAt;
        delete transaction.deletedAt;

        const transactionData = {
            '帳戶': accountName,
            '交易對象': entity.name + '(' + entity.type + ')',
            '交易類型': transaction.transactionType === 'income' ? '收入' : transaction.transactionType === 'expense' ? '支出' : '轉移',
            '收款/付款日期': transaction.paymentDate ? CommonUtils.formatDate(transaction.paymentDate, 'YYYY-MM-DD') : '',
            '憑證/發票日期': transaction.invoiceDate ? CommonUtils.formatDate(transaction.invoiceDate, 'YYYY-MM-DD') : '',
            '預計收/付款日期': transaction.expectedPaymentDate ? CommonUtils.formatDate(transaction.expectedPaymentDate, 'YYYY-MM-DD') : '',
            '交易項目內容': transaction.notes,
            '交易項目': transactionDescription,
            '金額': transaction.amount,
            '稅別': taxTypeName,
            '稅額': transaction.taxAmount,
            '手續費': transaction.feeAmount,
            '標籤': getTagsString(transaction.tags),
            '交易狀態': transaction.transactionStatus === 'completed' ? '已完成' : transaction.transactionStatus === 'pending' ? '待處理' : transaction.transactionStatus === 'cancelled' ? '已取消' : '未知',
            
        }

        return transactionData;
    }));



    //確認格式是否為Array
    if(!Array.isArray(transactions)){
        alert('匯出格式錯誤');
        return;
    }
    await exportExcel(transactions);
}

//--------------------------------標籤過濾功能--------------------------------

// 添加標籤到過濾列表
function addTagFilter(tagName) {
    if (!selectedTags.includes(tagName)) {
        selectedTags.push(tagName);
        updateTagFilterDisplay();
        applyCurrentFilters();
    }
}

// 從過濾列表移除標籤
function removeTagFilter(tagName) {
    const index = selectedTags.indexOf(tagName);
    if (index > -1) {
        selectedTags.splice(index, 1);
        updateTagFilterDisplay();
        applyCurrentFilters();
    }
}

// 切換標籤過濾狀態
function toggleTagFilter(tagName) {
    if (selectedTags.includes(tagName)) {
        removeTagFilter(tagName);
    } else {
        addTagFilter(tagName);
    }
}

// 清除所有標籤過濾
function clearAllTagFilters() {
    selectedTags = [];
    updateTagFilterDisplay();
    applyCurrentFilters();
}

// 更新標籤過濾顯示
function updateTagFilterDisplay() {
    const container = document.getElementById('selectedTagsContainer');
    const clearBtn = document.getElementById('clearTagsBtn');
    const resultInfo = document.getElementById('filterResultInfo');

    // 清空容器
    container.innerHTML = '';

    if (selectedTags.length === 0) {
        clearBtn.classList.add('hidden');
        resultInfo.textContent = '';
    } else {
        clearBtn.classList.remove('hidden');

        // 顯示選中的標籤
        selectedTags.forEach(tag => {
            const tagColor = getTagColor(tag);
            const tagElement = document.createElement('span');
            tagElement.className = `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${tagColor.textColor} ${tagColor.bgColor} ring-2 ring-blue-500 ring-opacity-50`;
            tagElement.innerHTML = `
                ${tag}
                <button type="button" class="ml-1 text-xs hover:text-red-600" onclick="removeTagFilter('${tag}')" title="移除此標籤過濾">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(tagElement);
        });

        // 顯示過濾結果資訊
        const filteredCount = filteredTransactions.length;
        resultInfo.textContent = `已選擇 ${selectedTags.length} 個標籤，找到 ${filteredCount} 筆相關交易`;
    }
}

// 應用當前的所有過濾條件
async function applyCurrentFilters() {
    const filters = {
        type: document.getElementById('typeFilter').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value
    };

    await loadTransactions(filters);
}

// 處理標籤點擊事件（需要在 updateDisplay 後綁定）
function bindTagClickEvents() {
    // 移除舊的事件監聽器並添加新的
    document.querySelectorAll('.tag-clickable').forEach(tagElement => {
        tagElement.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const tagName = this.getAttribute('data-tag');
            toggleTagFilter(tagName);
        });
    });
}

